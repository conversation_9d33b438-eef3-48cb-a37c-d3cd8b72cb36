package com.differ.wdgj.api.user.biz.domain.stock.notice.erp.processor.handler.filter;

import com.differ.wdgj.api.user.biz.domain.stock.data.StockContentResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockMultiWhsModeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.notice.ApiSysMatchNotice;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.multi.adapter.core.IMultiWarehouseAdapter;
import com.differ.wdgj.api.user.biz.domain.stock.notice.erp.processor.handler.INoticeProcessHandle;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.SyncStockNumRuleDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.SyncStockShopConfig;
import com.mchange.lang.IntegerUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * 普通库存同步匹配过滤器
 *
 * <AUTHOR>
 * @date 2024/12/30 下午7:54
 */
public class NormalStockNoticeFilter implements INoticeProcessHandle {
    /**
     * 匹配数据转换为[平台商品库存变动通知]
     *
     * @param apiSysMatchNotice 匹配信息扩展信息
     * @return 匹配处理结果
     */
    @Override
    public StockContentResult<?> process(StockSyncContext shopContext, ApiSysMatchNotice apiSysMatchNotice) {
        // 基础数据
        ApiSysMatchDO platGoodsMatch = apiSysMatchNotice.getPlatGoodsMatch();
        SyncStockShopConfig syncStockConfig = shopContext.getSyncStockConfig();
        IMultiWarehouseAdapter multiWhsAdapter = shopContext.getMultiWhsAdapter();

        // 匹配数据不完整
        if (platGoodsMatch == null || platGoodsMatch.getShopId() == 0) {
            return StockContentResult.failed("匹配记录数据不完整");
        }

        // 匹配状态过滤
        if (platGoodsMatch.getBstop()) {
            return StockContentResult.failed(String.format("匹配记录【%s】已停用", platGoodsMatch.getId()));
        }

        // 仅支持多仓库
        if(multiWhsAdapter.getMode(platGoodsMatch) == StockMultiWhsModeEnum.ONLY_MULTI_WAREHOUSE){
            return StockContentResult.failed("仅支持多仓库存同步");
        }

        // 不能保证触发数据返回erp仓库Id
        int erpWarehouseId = IntegerUtils.parseInt(apiSysMatchNotice.getWarehouseId(), 0);
        if(erpWarehouseId > 0){
            // 匹配级仓库判断
            String ruleWarehouse = platGoodsMatch.getRuleWarehouse();
            if(StringUtils.isNotEmpty(ruleWarehouse)){
                String[] matchWareHouseIds = ruleWarehouse.split(",");
                if(Arrays.stream(matchWareHouseIds).noneMatch(x -> x.equalsIgnoreCase(apiSysMatchNotice.getWarehouseId()))){
                    return StockContentResult.failed(String.format("变动仓库【%s】不在匹配配置中", apiSysMatchNotice.getWarehouseId()));
                }
            }

            // 店铺级仓库判断
            SyncStockNumRuleDto syncNumRule = syncStockConfig.getSyncNumRule();
            if(syncNumRule != null){
                String[] shopWareHouseIds = syncNumRule.getWarehouseIds();
                if(Arrays.stream(shopWareHouseIds).noneMatch(x -> x.equalsIgnoreCase(apiSysMatchNotice.getWarehouseId()))){
                    return StockContentResult.failed(String.format("变动仓库【%s】不在店铺配置中", apiSysMatchNotice.getWarehouseId()));
                }
            }
        }

        return StockContentResult.success();
    }
}

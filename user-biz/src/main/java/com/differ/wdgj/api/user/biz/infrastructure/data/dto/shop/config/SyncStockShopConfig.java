package com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config;

import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockSyncCreateModeEnum;

/**
 *  库存同步店铺配置实体
 *  </p> 注意：目前按需新增字段，实际数据库中已经有所有
 *
 * <AUTHOR>
 * @date 2024/11/11 下午3:16
 */
public class SyncStockShopConfig extends BaseShopConfig {

    /**
     * 获取或设置 启用自动同步库存
     */
    private boolean isEnableAutoSyncStock;

    /**
     * 获取或设置 同步0库存
     */
    private boolean isSyncZeroStock;

    /**
     * 获取或设置 同步数量大于0自动上架(淘宝)
     */
    private boolean isSyncMoreZeroAutoShelves;

    /**
     * 获取或设置 同步无效商品时自动删除
     */
    private boolean isSyncInvalidGoodsAutoDel;

    /**
     * 获取或设置 同步上次同步时失败的商品
     */
    private boolean isSyncLastTimeFailGoods;

    /**
     * 获取或设置 百分比库存后，如果还有小数点，去掉小数点，同时数量增加1
     */
    private boolean isDecimalPointAutoAddOne;

    /**
     * 获取或设置 同步所选仓库的独立库存（不减去唯品会JIT库存占用量）
     */
    private boolean isRemoveVPHStock;

    /**
     * 订单库存同步触发点
     */
    private StockSyncCreateModeEnum tradeSyncMode;

    /**
     * 同步数量规则
     */
    private SyncStockNumRuleDto syncNumRule;

    //region get/set
    public boolean getIsEnableAutoSyncStock() {
        return isEnableAutoSyncStock;
    }

    public void setIsEnableAutoSyncStock(boolean enableAutoSyncStock) {
        isEnableAutoSyncStock = enableAutoSyncStock;
    }

    public boolean isSyncZeroStock() {
        return isSyncZeroStock;
    }

    public void setSyncZeroStock(boolean syncZeroStock) {
        isSyncZeroStock = syncZeroStock;
    }

    public boolean isSyncMoreZeroAutoShelves() {
        return isSyncMoreZeroAutoShelves;
    }

    public void setSyncMoreZeroAutoShelves(boolean syncMoreZeroAutoShelves) {
        isSyncMoreZeroAutoShelves = syncMoreZeroAutoShelves;
    }

    public boolean isSyncInvalidGoodsAutoDel() {
        return isSyncInvalidGoodsAutoDel;
    }

    public void setSyncInvalidGoodsAutoDel(boolean syncInvalidGoodsAutoDel) {
        isSyncInvalidGoodsAutoDel = syncInvalidGoodsAutoDel;
    }

    public boolean isSyncLastTimeFailGoods() {
        return isSyncLastTimeFailGoods;
    }

    public void setSyncLastTimeFailGoods(boolean syncLastTimeFailGoods) {
        isSyncLastTimeFailGoods = syncLastTimeFailGoods;
    }

    public boolean isDecimalPointAutoAddOne() {
        return isDecimalPointAutoAddOne;
    }

    public void setDecimalPointAutoAddOne(boolean decimalPointAutoAddOne) {
        isDecimalPointAutoAddOne = decimalPointAutoAddOne;
    }

    public boolean isRemoveVPHStock() {
        return isRemoveVPHStock;
    }

    public void setRemoveVPHStock(boolean removeVPHStock) {
        isRemoveVPHStock = removeVPHStock;
    }

    public StockSyncCreateModeEnum getTradeSyncMode() {
        return tradeSyncMode;
    }

    public void setTradeSyncMode(StockSyncCreateModeEnum tradeSyncMode) {
        this.tradeSyncMode = tradeSyncMode;
    }

    public SyncStockNumRuleDto getSyncNumRule() {
        return syncNumRule;
    }

    public void setSyncNumRule(SyncStockNumRuleDto syncNumRule) {
        this.syncNumRule = syncNumRule;
    }
    //endregion
}

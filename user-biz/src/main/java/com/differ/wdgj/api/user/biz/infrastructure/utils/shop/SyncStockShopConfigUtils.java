package com.differ.wdgj.api.user.biz.infrastructure.utils.shop;

import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.SyncStockShopConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiShopConfigBizTypes;

/**
 * 库存同步店铺配置工具类
 *
 * <AUTHOR>
 * @date 2024/11/1 下午2:03
 */
public class SyncStockShopConfigUtils {
    //region 常量
    /**
     * 业务类型
     */
    private static final ApiShopConfigBizTypes TYPE = ApiShopConfigBizTypes.SYNC_STOCK;
    //endregion

    //region 构造
    private SyncStockShopConfigUtils() {
    }
    //endregion

    //region 获取配置

    /**
     * 根据店铺id获取库存同步店铺配置
     *
     * @param outAccount 外部会员名
     * @param shopId  外部店铺id
     * @return 店铺配置
     */
    public static SyncStockShopConfig singleByShopId(String outAccount, int shopId) {
        return (SyncStockShopConfig) ShopConfigUtils.getBizConfig(outAccount, TYPE, shopId);
    }

    //endregion
}
